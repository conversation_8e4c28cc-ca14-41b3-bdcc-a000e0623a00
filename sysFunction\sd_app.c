#include "mcu_cmic_gd32f470vet6.h"

FATFS fs;
FIL fdst;
uint16_t i = 0, count, result = 0;
UINT br, bw;

sd_card_info_struct sd_cardinfo;

BYTE buffer[128];
BYTE filebuffer[128];

ErrStatus memory_compare(uint8_t* src, uint8_t* dst, uint16_t length) 
{
    while(length --){
        if(*src++ != *dst++)
            return ERROR;
    }
    return SUCCESS;
}

void sd_fatfs_init(void)
{
    nvic_irq_enable(SDIO_IRQn, 0, 0);					// ʹ��SDIO�жϣ����ȼ�Ϊ0
}

/**
 * @brief       ͨ�����ڴ�ӡSD�������Ϣ
 * @param       ��
 * @retval      ��
 */
void card_info_get(void)
{
    sd_card_info_struct sd_cardinfo;      // SD����Ϣ�ṹ��
    sd_error_enum status;                 // SD������״̬
    uint32_t block_count, block_size;
    
    // ��ȡSD����Ϣ
    status = sd_card_information_get(&sd_cardinfo);
    
    if(SD_OK == status)
    {
        my_printf(DEBUG_USART, "\r\n*** SD Card Info ***\r\n");
        
        // ��ӡ������
        switch(sd_cardinfo.card_type)
        {
            case SDIO_STD_CAPACITY_SD_CARD_V1_1:
                my_printf(DEBUG_USART, "Card Type: Standard Capacity SD Card V1.1\r\n");
                break;
            case SDIO_STD_CAPACITY_SD_CARD_V2_0:
                my_printf(DEBUG_USART, "Card Type: Standard Capacity SD Card V2.0\r\n");
                break;
            case SDIO_HIGH_CAPACITY_SD_CARD:
                my_printf(DEBUG_USART, "Card Type: High Capacity SD Card\r\n");
                break;
            case SDIO_MULTIMEDIA_CARD:
                my_printf(DEBUG_USART, "Card Type: Multimedia Card\r\n");
                break;
            case SDIO_HIGH_CAPACITY_MULTIMEDIA_CARD:
                my_printf(DEBUG_USART, "Card Type: High Capacity Multimedia Card\r\n");
                break;
            case SDIO_HIGH_SPEED_MULTIMEDIA_CARD:
                my_printf(DEBUG_USART, "Card Type: High Speed Multimedia Card\r\n");
                break;
            default:
                my_printf(DEBUG_USART, "Card Type: Unknown\r\n");
                break;
        }
        
        // ��ӡ�������Ϳ��С
        block_count = (sd_cardinfo.card_csd.c_size + 1) * 1024;
        block_size = 512;
        my_printf(DEBUG_USART,"\r\n## Device size is %dKB (%.2fGB)##", sd_card_capacity_get(), sd_card_capacity_get() / 1024.0f / 1024.0f);
        my_printf(DEBUG_USART,"\r\n## Block size is %dB ##", block_size);
        my_printf(DEBUG_USART,"\r\n## Block count is %d ##", block_count);
        
        // ��ӡ������ID�Ͳ�Ʒ����
        my_printf(DEBUG_USART, "Manufacturer ID: 0x%X\r\n", sd_cardinfo.card_cid.mid);
        my_printf(DEBUG_USART, "OEM/Application ID: 0x%X\r\n", sd_cardinfo.card_cid.oid);
        
        // ��ӡ��Ʒ���� (PNM)
        uint8_t pnm[6];
        pnm[0] = (sd_cardinfo.card_cid.pnm0 >> 24) & 0xFF;
        pnm[1] = (sd_cardinfo.card_cid.pnm0 >> 16) & 0xFF;
        pnm[2] = (sd_cardinfo.card_cid.pnm0 >> 8) & 0xFF;
        pnm[3] = sd_cardinfo.card_cid.pnm0 & 0xFF;
        pnm[4] = sd_cardinfo.card_cid.pnm1 & 0xFF;
        pnm[5] = '\0';
        my_printf(DEBUG_USART, "Product Name: %s\r\n", pnm);
        
        // ��ӡ��Ʒ�汾�����к�
        my_printf(DEBUG_USART, "Product Revision: %d.%d\r\n", (sd_cardinfo.card_cid.prv >> 4) & 0x0F, sd_cardinfo.card_cid.prv & 0x0F);
        // ���к����޷��ŷ�ʽ��ʾ�����⸺��
        my_printf(DEBUG_USART, "Product Serial Number: 0x%08X\r\n", sd_cardinfo.card_cid.psn);
        
        // ��ӡCSD�汾������CSD��Ϣ
        my_printf(DEBUG_USART, "CSD Version: %d.0\r\n", sd_cardinfo.card_csd.csd_struct + 1);
        
    }
    else
    {
        my_printf(DEBUG_USART, "\r\nFailed to get SD card information, error code: %d\r\n", status);
    }
}

void sd_fatfs_test(void)
{
    uint16_t k = 5;
    DSTATUS stat = 0;
    do
    {
        stat = disk_initialize(0); 			//��ʼ��SD�����豸��0��,�������������,ÿ����������������Ӳ�̡�U �̵ȣ�ͨ����������һ��Ψһ�ı�š�
    }while((stat != 0) && (--k));			//�����ʼ��ʧ�ܣ��������k�Ρ�
    
    card_info_get();
    
    my_printf(DEBUG_USART, "SD Card disk_initialize:%d\r\n",stat);
    f_mount(0, &fs);						 //����SD�����ļ�ϵͳ���豸��0����
    my_printf(DEBUG_USART, "SD Card f_mount:%d\r\n",stat);
    
    if(RES_OK == stat)						 //���ع��ؽ����FR_OK ��ʾ�ɹ�����
    {        
        my_printf(DEBUG_USART, "\r\nSD Card Initialize Success!\r\n");
     
        result = f_open(&fdst, "0:/FATFS.TXT", FA_CREATE_ALWAYS | FA_WRITE);		//��SD���ϴ����ļ�FATFS.TXT��
     
        sprintf((char *)filebuffer, "HELLO MCUSTUDIO");

        //result = f_write(&fdst, textfilebuffer, sizeof(textfilebuffer), &bw); 	//��textfilebuffer�е�����д���ļ���
        result = f_write(&fdst, filebuffer, sizeof(filebuffer), &bw);				//��filebuffer�е�����д���ļ���
        
        /**********���д���� begin****************/
        if(FR_OK == result)		
            my_printf(DEBUG_USART, "FATFS FILE write Success!\r\n");
        else
        {
            my_printf(DEBUG_USART, "FATFS FILE write failed!\r\n");
        }
        /**********���д���� end****************/
        
        f_close(&fdst);//�ر��ļ�
        
        
        f_open(&fdst, "0:/FATFS.TXT", FA_OPEN_EXISTING | FA_READ);	//��ֻ����ʽ���´��ļ�
        br = 1;
        
        /**********ѭ����ȡ�ļ����� begin****************/
        for(;;)
        {
            // ��ջ�����
            for (count=0; count<128; count++)
            {
                buffer[count]=0;
            }
            // ��ȡ�ļ����ݵ�buffer
            result = f_read(&fdst, buffer, sizeof(buffer), &br);
            if ((0 == result)|| (0 == br))
            {
                break;
            }
        }
        /**********ѭ����ȡ�ļ����� end****************/
        
        // �Ƚ϶�ȡ��������д��������Ƿ�һ��
        if(SUCCESS == memory_compare(buffer, filebuffer, 128))
        {
            my_printf(DEBUG_USART, "FATFS Read File Success!\r\nThe content is:%s\r\n",buffer);
        }
        else
        {
            my_printf(DEBUG_USART, "FATFS FILE read failed!\n");            
        }
         f_close(&fdst);//�ر��ļ�
    }
}

// Storage system implementation
#include "sd_app.h"
#include "usart_app.h"
#include "sampling_app.h"

// External function declarations (from sampling_app.c)
extern uint8_t bcd_to_dec(uint8_t bcd_value);
extern uint8_t rtc_month_to_decimal(uint8_t rtc_month);
extern void format_sample_time(char *buffer, rtc_parameter_struct *rtc_time);
extern uint32_t rtc_to_unix_timestamp(rtc_parameter_struct *rtc_time);
extern void format_timestamp_hex(uint32_t timestamp, char *hex_buffer);
extern void format_voltage_hex(float voltage, char *hex_buffer);

// LFN support functions for FATFS
#if _USE_LFN
/*!
    \brief      Unicode conversion function for LFN support
    \param[in]  chr: character to convert
    \param[in]  dir: conversion direction (0: Unicode to OEM, 1: OEM to Unicode)
    \param[out] none
    \retval     converted character
*/
WCHAR ff_convert(WCHAR chr, UINT dir)
{
    // Simple pass-through for ASCII characters
    if(chr < 0x80) {
        return chr;
    }

    // For non-ASCII, return as-is (basic implementation)
    return chr;
}

/*!
    \brief      Unicode upper case conversion function for LFN support
    \param[in]  chr: character to convert to upper case
    \param[out] none
    \retval     upper case character
*/
WCHAR ff_wtoupper(WCHAR chr)
{
    // Convert ASCII lowercase to uppercase
    if(chr >= 'a' && chr <= 'z') {
        return chr - 'a' + 'A';
    }

    // For non-ASCII or already uppercase, return as-is
    return chr;
}
#endif

// Global storage system state
static storage_system_t g_storage_system = {0};

// Log output control flag
static bool suppress_file_logs = false;

// Forward declarations
static bool create_storage_directories(void);

/*!
    \brief      check SD card availability
    \param[in]  none
    \param[out] none
    \retval     true if SD card is available, false otherwise
*/
static bool check_sd_availability(void)
{
    DSTATUS status = disk_status(0);
    return (status == 0); // 0表示SD卡可用
}

/*!
    \brief      initialize file manager for specific storage type
    \param[in]  type: storage type to initialize
    \param[out] none
    \retval     none
*/
static void init_file_manager(storage_type_t type)
{
    if(type >= 4) return; // 防止数组越界

    file_manager_t *manager = &g_storage_system.managers[type];

    // 初始化文件管理器状态
    memset(manager->current_filename, 0, sizeof(manager->current_filename));
    manager->record_count = 0;
    manager->file_open = false;
    // file_handle不需要初始化，使用时会重新设置
}

/*!
    \brief      initialize storage system
    \param[in]  none
    \param[out] none
    \retval     true if initialization successful, false otherwise
*/
bool storage_init(void)
{
    FRESULT result;

    // 检查是否已经初始化
    if(g_storage_system.initialized) {
        return true;
    }

    // 检查SD卡可用性
    g_storage_system.sd_available = check_sd_availability();
    if(!g_storage_system.sd_available) {
        my_printf(DEBUG_USART, "Storage init failed: SD card not available\r\n");
        return false;
    }

    // 挂载文件系统
    result = f_mount(0, &fs);
    if(result != FR_OK) {
        my_printf(DEBUG_USART, "Storage init failed: f_mount error %d\r\n", result);
        g_storage_system.sd_available = false;
        return false;
    }

    // 创建存储目录
    if(!create_storage_directories()) {
        my_printf(DEBUG_USART, "Storage init warning: Some directories creation failed\r\n");
        // 继续初始化，目录创建失败不是致命错误
    }

    // 初始化所有文件管理器
    for(int i = 0; i < 4; i++) {
        init_file_manager((storage_type_t)i);
    }

    // 标记为已初始化
    g_storage_system.initialized = true;

    my_printf(DEBUG_USART, "Storage system initialized successfully\r\n");
    return true;
}

/*!
    \brief      check if storage system is available
    \param[in]  none
    \param[out] none
    \retval     true if storage is available, false otherwise
*/
bool storage_is_available(void)
{
    if(!g_storage_system.initialized) {
        return false;
    }

    // 重新检查SD卡状态
    g_storage_system.sd_available = check_sd_availability();
    return g_storage_system.sd_available;
}

/*!
    \brief      deinitialize storage system
    \param[in]  none
    \param[out] none
    \retval     none
*/
void storage_deinit(void)
{
    if(!g_storage_system.initialized) {
        return;
    }

    // 关闭所有打开的文件
    for(int i = 0; i < 4; i++) {
        file_manager_t *manager = &g_storage_system.managers[i];
        if(manager->file_open) {
            f_close(&manager->file_handle);
            manager->file_open = false;
        }
    }

    // 卸载文件系统
    f_mount(0, NULL);

    // 重置状态
    memset(&g_storage_system, 0, sizeof(storage_system_t));

    my_printf(DEBUG_USART, "Storage system deinitialized\r\n");
}

// Directory and file naming implementation

// Storage directory paths
static const char* storage_dirs[] = {
    "0:/sample",      // STORAGE_TYPE_SAMPLE
    "0:/overLimit",   // STORAGE_TYPE_OVERLIMIT
    "0:/log",         // STORAGE_TYPE_LOG
    "0:/hideData"     // STORAGE_TYPE_HIDEDATA
};

/*!
    \brief      create storage directories
    \param[in]  none
    \param[out] none
    \retval     true if all directories created successfully, false otherwise
*/
static bool create_storage_directories(void)
{
    FRESULT result;
    bool success = true;

    // Create directories with special handling for case sensitivity
    for(int i = 0; i < 4; i++) {
        result = f_mkdir(storage_dirs[i]);

        // FR_EXIST is acceptable (directory already exists)
        if(result != FR_OK && result != FR_EXIST) {
            my_printf(DEBUG_USART, "Failed to create directory %s, error: %d\r\n",
                      storage_dirs[i], result);
            success = false;
        } else {
            my_printf(DEBUG_USART, "Directory %s ready\r\n", storage_dirs[i]);
        }
    }

    return success;
}

/*!
    \brief      generate 14-digit datetime string from current RTC time
    \param[in]  datetime_str: output buffer for datetime string (must be at least 15 bytes)
    \param[out] none
    \retval     true if successful, false otherwise
*/
static bool generate_datetime_string(char *datetime_str)
{
    rtc_parameter_struct rtc_time;

    if(datetime_str == NULL) {
        return false;
    }

    // Get current RTC time
    rtc_current_time_get(&rtc_time);

    // Convert BCD values to decimal (reuse existing functions from sampling_app.c)
    int year = 2000 + bcd_to_dec(rtc_time.year);
    int month = rtc_month_to_decimal(rtc_time.month);
    int day = bcd_to_dec(rtc_time.date);
    int hour = bcd_to_dec(rtc_time.hour);
    int minute = bcd_to_dec(rtc_time.minute);
    int second = bcd_to_dec(rtc_time.second);

    // Format as 14-digit string: YYYYMMDDHHMMSS
    sprintf(datetime_str, "%04d%02d%02d%02d%02d%02d",
            year, month, day, hour, minute, second);

    return true;
}

/*!
    \brief      generate sample data filename
    \param[in]  filename: output buffer for filename (must be at least 64 bytes)
    \param[out] none
    \retval     true if successful, false otherwise
*/
static bool generate_sample_filename(char *filename)
{
    char datetime_str[15];

    if(filename == NULL) {
        return false;
    }

    if(!generate_datetime_string(datetime_str)) {
        return false;
    }

    sprintf(filename, "0:/sample/sampleData%s.txt", datetime_str);
    return true;
}

/*!
    \brief      generate overlimit data filename
    \param[in]  filename: output buffer for filename (must be at least 64 bytes)
    \param[out] none
    \retval     true if successful, false otherwise
*/
static bool generate_overlimit_filename(char *filename)
{
    char datetime_str[15];

    if(filename == NULL) {
        return false;
    }

    if(!generate_datetime_string(datetime_str)) {
        return false;
    }

    sprintf(filename, "0:/overLimit/overlimit%s.txt", datetime_str);
    return true;
}

/*!
    \brief      generate log filename
    \param[in]  filename: output buffer for filename (must be at least 64 bytes)
    \param[out] none
    \retval     true if successful, false otherwise
*/
static bool generate_log_filename(char *filename)
{
    uint32_t log_id;

    if(filename == NULL) {
        return false;
    }

    // Get current log ID from configuration
    log_id = get_log_id();

    sprintf(filename, "0:/log/log%lu.txt", log_id);
    return true;
}

/*!
    \brief      generate hidedata filename
    \param[in]  filename: output buffer for filename (must be at least 64 bytes)
    \param[out] none
    \retval     true if successful, false otherwise
*/
static bool generate_hidedata_filename(char *filename)
{
    char datetime_str[15];

    if(filename == NULL) {
        return false;
    }

    if(!generate_datetime_string(datetime_str)) {
        return false;
    }

    sprintf(filename, "0:/hideData/hidedata%s.txt", datetime_str);
    return true;
}

/*!
    \brief      check if file exists
    \param[in]  filename: filename to check
    \param[out] none
    \retval     true if file exists, false otherwise
*/
static bool file_exists(const char *filename)
{
    FIL test_file;
    FRESULT result;

    if(filename == NULL) {
        return false;
    }

    result = f_open(&test_file, filename, FA_READ);
    if(result == FR_OK) {
        f_close(&test_file);
        return true;
    }

    return false;
}

/*!
    \brief      generate unique filename for storage type
    \param[in]  type: storage type
    \param[in]  filename: output buffer for filename (must be at least 64 bytes)
    \param[out] none
    \retval     true if successful, false otherwise
*/
static bool generate_unique_filename(storage_type_t type, char *filename)
{
    bool success = false;
    int retry_count = 0;
    const int max_retries = 10;

    if(filename == NULL || type >= 4) {
        return false;
    }

    // Try to generate unique filename
    while(retry_count < max_retries) {
        switch(type) {
            case STORAGE_TYPE_SAMPLE:
                success = generate_sample_filename(filename);
                break;
            case STORAGE_TYPE_OVERLIMIT:
                success = generate_overlimit_filename(filename);
                break;
            case STORAGE_TYPE_LOG:
                success = generate_log_filename(filename);
                break;
            case STORAGE_TYPE_HIDEDATA:
                success = generate_hidedata_filename(filename);
                break;
            default:
                return false;
        }

        if(!success) {
            return false;
        }

        // Check if file already exists
        if(!file_exists(filename)) {
            return true; // Unique filename found
        }

        // For log files, use the current power-on count (don't increment)
        // Each power-on session uses one log file
        if(type == STORAGE_TYPE_LOG) {
            return true; // Use the current power-on count filename
        } else {
            // For other types, wait a second and try again
            // In embedded system, we can't easily wait, so we add a counter
            retry_count++;
            if(retry_count >= max_retries) {
                my_printf(DEBUG_USART, "Warning: Could not generate unique filename for type %d\r\n", type);
                return true; // Use the filename anyway
            }
        }
    }

    return false;
}

// File management and data writing implementation

/*!
    \brief      open storage file for specific type
    \param[in]  type: storage type
    \param[out] none
    \retval     true if successful, false otherwise
*/
static bool open_storage_file(storage_type_t type)
{
    file_manager_t *manager;
    FRESULT result;

    if(type >= 4 || !g_storage_system.initialized) {
        return false;
    }

    manager = &g_storage_system.managers[type];

    // Close existing file if open
    if(manager->file_open) {
        f_close(&manager->file_handle);
        manager->file_open = false;
    }

    // Generate unique filename
    if(!generate_unique_filename(type, manager->current_filename)) {
        my_printf(DEBUG_USART, "Failed to generate filename for storage type %d\r\n", type);
        return false;
    }

    // Open file for writing (create if not exists, overwrite if exists)
    result = f_open(&manager->file_handle, manager->current_filename,
                    FA_WRITE | FA_CREATE_ALWAYS);

    if(result != FR_OK) {
        // Special handling for SD card not available error (error 3)
        if(result == 3 && !storage_is_available()) {
            return false; // Silent failure for SD card unavailable
        }

        if(!suppress_file_logs) {
            my_printf(DEBUG_USART, "Failed to open file %s, error: %d\r\n",
                      manager->current_filename, result);
        }
        return false;
    }

    manager->file_open = true;
    manager->record_count = 0;

    if(!suppress_file_logs) {
        my_printf(DEBUG_USART, "Opened storage file: %s\r\n", manager->current_filename);
    }
    return true;
}

/*!
    \brief      close storage file for specific type
    \param[in]  type: storage type
    \param[out] none
    \retval     true if successful, false otherwise
*/
static bool close_storage_file(storage_type_t type)
{
    file_manager_t *manager;
    FRESULT result;

    if(type >= 4 || !g_storage_system.initialized) {
        return false;
    }

    manager = &g_storage_system.managers[type];

    if(!manager->file_open) {
        return true; // Already closed
    }

    result = f_close(&manager->file_handle);
    if(result != FR_OK) {
        my_printf(DEBUG_USART, "Failed to close file %s, error: %d\r\n",
                  manager->current_filename, result);
        return false;
    }

    manager->file_open = false;
    my_printf(DEBUG_USART, "Closed storage file: %s\r\n", manager->current_filename);
    return true;
}

/*!
    \brief      check if file record limit is reached
    \param[in]  type: storage type
    \param[out] none
    \retval     true if limit reached, false otherwise
*/
static bool check_file_record_limit(storage_type_t type)
{
    file_manager_t *manager;

    if(type >= 4 || !g_storage_system.initialized) {
        return true; // Treat as limit reached for safety
    }

    manager = &g_storage_system.managers[type];
    return (manager->record_count >= 10); // 10 records per file
}

/*!
    \brief      write data to storage file
    \param[in]  type: storage type
    \param[in]  data: data string to write
    \param[out] none
    \retval     true if successful, false otherwise
*/
static bool write_data_to_file(storage_type_t type, const char *data)
{
    file_manager_t *manager;
    FRESULT result;
    UINT bytes_written;
    bool original_suppress_flag;

    if(type >= 4 || !g_storage_system.initialized || data == NULL) {
        return false;
    }

    manager = &g_storage_system.managers[type];

    // Set suppress flag for log operations to reduce noise
    original_suppress_flag = suppress_file_logs;
    if(type == STORAGE_TYPE_LOG) {
        suppress_file_logs = true;
    }

    // Check if file is open
    if(!manager->file_open) {
        if(!open_storage_file(type)) {
            suppress_file_logs = original_suppress_flag; // Restore flag
            return false;
        }
    }

    // Check if record limit reached, create new file if needed
    if(check_file_record_limit(type)) {
        close_storage_file(type);
        if(!open_storage_file(type)) {
            suppress_file_logs = original_suppress_flag; // Restore flag
            return false;
        }
    }

    // Write data to file
    result = f_write(&manager->file_handle, data, strlen(data), &bytes_written);
    if(result != FR_OK || bytes_written != strlen(data)) {
        my_printf(DEBUG_USART, "Failed to write data to file %s, error: %d\r\n",
                  manager->current_filename, result);
        suppress_file_logs = original_suppress_flag; // Restore flag
        return false;
    }

    // Write newline
    result = f_write(&manager->file_handle, "\r\n", 2, &bytes_written);
    if(result != FR_OK || bytes_written != 2) {
        my_printf(DEBUG_USART, "Failed to write newline to file %s, error: %d\r\n",
                  manager->current_filename, result);
        suppress_file_logs = original_suppress_flag; // Restore flag
        return false;
    }

    // Sync file to ensure data is written
    f_sync(&manager->file_handle);

    // Increment record count
    manager->record_count++;

    // Restore suppress flag
    suppress_file_logs = original_suppress_flag;

    return true;
}

// Data formatting functions

/*!
    \brief      format sample data for storage
    \param[in]  voltage: voltage value
    \param[in]  over_limit: whether voltage is over limit
    \param[in]  buffer: output buffer for formatted data (must be at least 64 bytes)
    \param[out] none
    \retval     true if successful, false otherwise
*/
static bool format_sample_data(float voltage, bool over_limit, char *buffer)
{
    rtc_parameter_struct rtc_time;
    char time_str[32];

    if(buffer == NULL) {
        return false;
    }

    // Get current time
    rtc_current_time_get(&rtc_time);
    format_sample_time(time_str, &rtc_time);

    // Format: 2025-01-01 00:30:10 1.5V
    sprintf(buffer, "%s %.1fV", time_str, voltage);

    return true;
}

/*!
    \brief      format overlimit data for storage
    \param[in]  voltage: voltage value
    \param[in]  buffer: output buffer for formatted data (must be at least 64 bytes)
    \param[out] none
    \retval     true if successful, false otherwise
*/
static bool format_overlimit_data(float voltage, char *buffer)
{
    rtc_parameter_struct rtc_time;
    char time_str[32];
    config_params_t *config;

    if(buffer == NULL) {
        return false;
    }

    // Get current time
    rtc_current_time_get(&rtc_time);
    format_sample_time(time_str, &rtc_time);

    config = get_config_params();
    // Format: 2025-01-01 00:30:10 30V limit 10V
    sprintf(buffer, "%s %.0fV limit %.0fV", time_str, voltage, config->limit);

    return true;
}

/*!
    \brief      format log data for storage
    \param[in]  operation_desc: operation description
    \param[in]  buffer: output buffer for formatted data (must be at least 128 bytes)
    \param[out] none
    \retval     true if successful, false otherwise
*/
static bool format_log_data(const char *operation_desc, char *buffer)
{
    rtc_parameter_struct rtc_time;
    char time_str[32];

    if(buffer == NULL || operation_desc == NULL) {
        return false;
    }

    // Get current time
    rtc_current_time_get(&rtc_time);
    format_sample_time(time_str, &rtc_time);

    sprintf(buffer, "%s %s", time_str, operation_desc);

    return true;
}

/*!
    \brief      format hidedata (encrypted) for storage
    \param[in]  voltage: voltage value
    \param[in]  over_limit: whether voltage is over limit
    \param[in]  buffer: output buffer for formatted data (must be at least 32 bytes)
    \param[out] none
    \retval     true if successful, false otherwise
*/
static bool format_hidedata(float voltage, bool over_limit, char *buffer)
{
    rtc_parameter_struct rtc_time;
    char timestamp_hex[9], voltage_hex[9];
    uint32_t timestamp;

    if(buffer == NULL) {
        return false;
    }

    // Get current time and convert to Unix timestamp
    rtc_current_time_get(&rtc_time);
    timestamp = rtc_to_unix_timestamp(&rtc_time);

    // Format timestamp and voltage as HEX
    format_timestamp_hex(timestamp, timestamp_hex);
    format_voltage_hex(voltage, voltage_hex);

    // Format HEX data with optional over-limit marker
    if(over_limit) {
        sprintf(buffer, "%s%s*", timestamp_hex, voltage_hex);
    } else {
        sprintf(buffer, "%s%s", timestamp_hex, voltage_hex);
    }

    return true;
}

// Storage interface implementation

/*!
    \brief      save sample data to storage
    \param[in]  voltage: voltage value
    \param[in]  over_limit: whether voltage is over limit
    \param[out] none
    \retval     true if successful, false otherwise
*/
bool storage_save_sample_data(float voltage, bool over_limit)
{
    char data_buffer[128];
    storage_type_t storage_type;
    bool encrypt_mode;

    // Check if storage system is available
    if(!storage_is_available()) {
        return false; // Fail silently if storage not available
    }

    // Get encrypt mode from configuration
    encrypt_mode = get_encrypt_mode();

    // Determine storage type and format based on encrypt mode
    if(encrypt_mode) {
        // Encrypt mode (hide): store ONLY to hideData folder using HEX format
        // Sample folder will NOT contain any data in hide mode
        storage_type = STORAGE_TYPE_HIDEDATA;
        if(!format_hidedata(voltage, over_limit, data_buffer)) {
            return false;
        }
    } else {
        // Normal mode: store to sample folder using standard format
        storage_type = STORAGE_TYPE_SAMPLE;
        if(!format_sample_data(voltage, over_limit, data_buffer)) {
            return false;
        }
    }

    // Write data to file
    return write_data_to_file(storage_type, data_buffer);
}

/*!
    \brief      save overlimit data to storage
    \param[in]  voltage: voltage value
    \param[out] none
    \retval     true if successful, false otherwise
*/
bool storage_save_overlimit_data(float voltage)
{
    char data_buffer[128];

    // Check if storage system is available
    if(!storage_is_available()) {
        return false; // Fail silently if storage not available
    }

    // Format overlimit data (always use standard format)
    if(!format_overlimit_data(voltage, data_buffer)) {
        return false;
    }

    // Write data to overlimit file (always store regardless of encrypt mode)
    // This ensures overlimit data is always available in overlimit folder
    // even when hide mode is enabled
    return write_data_to_file(STORAGE_TYPE_OVERLIMIT, data_buffer);
}

/*!
    \brief      log operation to storage
    \param[in]  operation_desc: operation description
    \param[out] none
    \retval     true if successful, false otherwise
*/
bool storage_log_operation(const char* operation_desc)
{
    char data_buffer[128];

    // Check parameters
    if(operation_desc == NULL || strlen(operation_desc) == 0) {
        return false;
    }

    // Check if storage system is available
    if(!storage_is_available()) {
        return false; // Fail silently if storage not available
    }

    // Format log data
    if(!format_log_data(operation_desc, data_buffer)) {
        return false;
    }

    // Write data to log file
    return write_data_to_file(STORAGE_TYPE_LOG, data_buffer);
}



/*!
    \brief      set encrypt mode
    \param[in]  enabled: true to enable encrypt mode, false to disable
    \param[out] none
    \retval     true if successful, false otherwise
*/
bool storage_set_encrypt_mode(bool enabled)
{
    // Use existing configuration management function
    set_encrypt_mode(enabled);

    my_printf(DEBUG_USART, "Storage encrypt mode %s\r\n",
              enabled ? "enabled" : "disabled");

    return true;
}

/*!
    \brief      get current encrypt mode
    \param[in]  none
    \param[out] none
    \retval     true if encrypt mode enabled, false otherwise
*/
bool storage_get_encrypt_mode(void)
{
    // Use existing configuration management function
    return get_encrypt_mode();
}

// Storage system testing and monitoring implementation

/*!
    \brief      test storage system functionality
    \param[in]  none
    \param[out] none
    \retval     true if all tests pass, false otherwise
*/
bool storage_test(void)
{
    bool all_tests_passed = true;

    my_printf(DEBUG_USART, "=== Storage System Test ===\r\n");

    // Test 1: Storage system initialization
    my_printf(DEBUG_USART, "Test 1: Storage initialization...");
    if(storage_is_available()) {
        my_printf(DEBUG_USART, "PASS\r\n");
    } else {
        my_printf(DEBUG_USART, "FAIL\r\n");
        all_tests_passed = false;
    }

    // Test 2: Directory structure verification
    my_printf(DEBUG_USART, "Test 2: Directory structure...");
    if(create_storage_directories()) {
        my_printf(DEBUG_USART, "PASS\r\n");
    } else {
        my_printf(DEBUG_USART, "FAIL\r\n");
        all_tests_passed = false;
    }

    // Test 3: Sample data storage
    my_printf(DEBUG_USART, "Test 3: Sample data storage...");
    if(storage_save_sample_data(12.34f, false)) {
        my_printf(DEBUG_USART, "PASS\r\n");
    } else {
        my_printf(DEBUG_USART, "FAIL\r\n");
        all_tests_passed = false;
    }

    // Test 4: Overlimit data storage
    my_printf(DEBUG_USART, "Test 4: Overlimit data storage...");
    if(storage_save_overlimit_data(150.75f)) {
        my_printf(DEBUG_USART, "PASS\r\n");
    } else {
        my_printf(DEBUG_USART, "FAIL\r\n");
        all_tests_passed = false;
    }

    // Test 5: Log operation
    my_printf(DEBUG_USART, "Test 5: Log operation...");
    if(storage_log_operation("Storage system test executed")) {
        my_printf(DEBUG_USART, "PASS\r\n");
    } else {
        my_printf(DEBUG_USART, "FAIL\r\n");
        all_tests_passed = false;
    }

    // Test 6: Encrypt mode control
    my_printf(DEBUG_USART, "Test 6: Encrypt mode control...");
    bool original_mode = storage_get_encrypt_mode();
    storage_set_encrypt_mode(!original_mode);
    if(storage_get_encrypt_mode() == !original_mode) {
        storage_set_encrypt_mode(original_mode); // Restore original mode
        my_printf(DEBUG_USART, "PASS\r\n");
    } else {
        my_printf(DEBUG_USART, "FAIL\r\n");
        all_tests_passed = false;
    }

    // Test summary
    my_printf(DEBUG_USART, "=== Test Summary ===\r\n");
    if(all_tests_passed) {
        my_printf(DEBUG_USART, "All tests PASSED\r\n");
        storage_log_operation("Storage system test: ALL PASSED");
    } else {
        my_printf(DEBUG_USART, "Some tests FAILED\r\n");
        storage_log_operation("Storage system test: SOME FAILED");
    }

    return all_tests_passed;
}

/*!
    \brief      get storage system status information
    \param[in]  none
    \param[out] none
    \retval     none
*/
void storage_get_status(void)
{
    FATFS *fs_ptr;
    DWORD free_clusters, total_sectors, free_sectors;
    FRESULT result;

    my_printf(DEBUG_USART, "=== Storage System Status ===\r\n");

    // Check if storage system is available
    if(!storage_is_available()) {
        my_printf(DEBUG_USART, "Storage system: NOT AVAILABLE\r\n");
        return;
    }

    my_printf(DEBUG_USART, "Storage system: AVAILABLE\r\n");

    // Get disk space information
    result = f_getfree("0:", &free_clusters, &fs_ptr);
    if(result == FR_OK) {
        total_sectors = (fs_ptr->n_fatent - 2) * fs_ptr->csize;
        free_sectors = free_clusters * fs_ptr->csize;

        my_printf(DEBUG_USART, "Total space: %lu KB\r\n", total_sectors / 2);
        my_printf(DEBUG_USART, "Free space: %lu KB\r\n", free_sectors / 2);
        my_printf(DEBUG_USART, "Used space: %lu KB\r\n", (total_sectors - free_sectors) / 2);
    } else {
        my_printf(DEBUG_USART, "Failed to get disk space info, error: %d\r\n", result);
    }

    // Show file manager status
    for(int i = 0; i < 4; i++) {
        file_manager_t *manager = &g_storage_system.managers[i];
        const char* type_names[] = {"SAMPLE", "OVERLIMIT", "LOG", "HIDEDATA"};

        my_printf(DEBUG_USART, "%s: %s, Records: %lu\r\n",
                  type_names[i],
                  manager->file_open ? "OPEN" : "CLOSED",
                  manager->record_count);
    }

    // Show encrypt mode status
    my_printf(DEBUG_USART, "Encrypt mode: %s\r\n",
              get_encrypt_mode() ? "ENABLED" : "DISABLED");

    my_printf(DEBUG_USART, "Log ID: %lu\r\n", get_log_id());
}

/*!
    \brief      gracefully shutdown storage system
    \param[in]  none
    \param[out] none
    \retval     true if shutdown successful, false otherwise
*/
bool storage_shutdown(void)
{
    my_printf(DEBUG_USART, "Storage system shutting down...\r\n");

    // Log shutdown operation
    storage_log_operation("Storage system shutdown initiated");

    // Close all open files and cleanup
    storage_deinit();

    my_printf(DEBUG_USART, "Storage system shutdown complete\r\n");
    return true;
}
