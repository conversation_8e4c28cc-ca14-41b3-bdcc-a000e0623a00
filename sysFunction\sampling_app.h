#ifndef SAMPLING_APP_H
#define SAMPLING_APP_H

#include "stdint.h"
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// Sampling state enumeration
typedef enum {
    SAMPLING_IDLE = 0,
    SAMPLING_ACTIVE
} sampling_state_t;

// Sampling control structure
typedef struct {
    sampling_state_t state;         // Current sampling state
    uint8_t cycle_seconds;          // Sampling cycle in seconds (5/10/15)
    uint32_t last_sample_time;      // Last sampling timestamp
    uint32_t led_blink_time;        // LED blink timestamp
    float current_voltage;          // Current voltage value
    bool over_limit;               // Over limit status
} sampling_control_t;

// Function declarations
void sampling_task(void);
void sampling_start_handler(void);
void sampling_stop_handler(void);
void sampling_init(void);

// ADC voltage conversion functions
float adc_to_voltage(uint16_t adc_value);
float get_current_voltage(void);

// Time formatting functions
void format_sample_time(char *buffer, rtc_parameter_struct *rtc_time);
void format_display_time(char *buffer, rtc_parameter_struct *rtc_time);
void get_current_time_string(char *buffer, bool full_format);

// Time conversion utility functions
uint8_t bcd_to_dec(uint8_t bcd_value);
uint8_t rtc_month_to_decimal(uint8_t rtc_month);

// Unix timestamp conversion functions
uint32_t rtc_to_unix_timestamp(rtc_parameter_struct *rtc_time);
void format_timestamp_hex(uint32_t timestamp, char *hex_buffer);

// Voltage HEX encoding functions
void format_voltage_hex(float voltage, char *hex_buffer);

// LED status control functions
void update_led_status(void);
void update_overlimit_led(void);

// OLED display functions moved to oled_app.h

// Sampling logic and over-limit detection functions
bool is_sample_time(void);
bool check_over_limit(float voltage);
void output_sample_data(float voltage, bool over_limit);
void perform_sample(void);

// Button event handling functions
void toggle_sampling_state(void);
void set_sampling_cycle(uint8_t cycle);
void sampling_btn_event_handler(ebtn_btn_t *btn, ebtn_evt_t evt);

// Configuration management functions
void load_sampling_config(void);
void save_sampling_config(void);

// Access functions for other modules
sampling_state_t get_sampling_state(void);
float get_current_voltage_value(void);

// State query functions
sampling_state_t get_sampling_state(void);
uint8_t get_sampling_cycle(void);
float get_current_voltage_value(void);
bool is_over_limit(void);

#ifdef __cplusplus
}
#endif

#endif // SAMPLING_APP_H
