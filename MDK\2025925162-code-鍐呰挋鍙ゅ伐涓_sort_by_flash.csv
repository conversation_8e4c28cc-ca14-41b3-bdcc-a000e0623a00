File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,18.411116%,13263,96,12712,551,0,96
usart_app.o,8.671812%,6247,2624,6006,177,64,2560
sdio_sdcard.o,8.439990%,6080,64,6048,0,32,32
ff.o,8.135983%,5861,520,5840,13,8,512
oled.o,5.372165%,3870,22,1136,2712,22,0
sd_app.o,3.749410%,2701,3880,2624,45,32,3848
btod.o,2.987312%,2152,0,2152,0,0,0
sampling_app.o,2.809628%,2024,20,1956,48,20,0
mcu_cmic_gd32f470vet6.o,2.679142%,1930,1602,1908,0,22,1580
ebtn.o,2.362642%,1702,60,1702,0,0,60
fz_wm.l,2.112774%,1522,0,1506,16,0,0
scanf_fp.o,1.765735%,1272,0,1272,0,0,0
_printf_fp_dec.o,1.463117%,1054,0,1054,0,0,0
gd25qxx.o,1.238235%,892,0,892,0,0,0
_scanf.o,1.227130%,884,0,884,0,0,0
_printf_fp_hex.o,1.113301%,802,0,764,38,0,0
scanf_hexfp.o,1.110525%,800,0,800,0,0,0
m_wm.l,1.107749%,798,0,798,0,0,0
gd32f4xx_rcu.o,1.027236%,740,0,740,0,0,0
gd32f4xx_dma.o,0.974486%,702,0,702,0,0,0
perf_counter.o,0.913407%,658,64,590,4,64,0
gd32f4xx_usart.o,0.863433%,622,0,622,0,0,0
gd32f4xx_timer.o,0.813460%,586,0,586,0,0,0
gd32f4xx_sdio.o,0.757933%,546,0,546,0,0,0
gd32f4xx_adc.o,0.724618%,522,0,522,0,0,0
btn_app.o,0.721841%,520,196,310,14,196,0
system_gd32f4xx.o,0.719065%,518,4,514,0,4,0
startup_gd32f450_470.o,0.682973%,492,2048,64,428,0,2048
gd32f4xx_i2c.o,0.608012%,438,0,438,0,0,0
__printf_flags_ss_wp.o,0.567756%,409,0,392,17,0,0
oled_app.o,0.566368%,408,40,400,0,8,32
gd32f4xx_rtc.o,0.566368%,408,0,408,0,0,0
bigflt0.o,0.521947%,376,0,228,148,0,0
dmul.o,0.471973%,340,0,340,0,0,0
_scanf_int.o,0.460868%,332,0,332,0,0,0
lc_ctype_c.o,0.438657%,316,0,44,272,0,0
scanf_infnan.o,0.427552%,308,0,308,0,0,0
gd32f4xx_it.o,0.405342%,292,0,292,0,0,0
narrow.o,0.369250%,266,0,266,0,0,0
gd32f4xx_gpio.o,0.366473%,264,0,264,0,0,0
diskio.o,0.349815%,252,0,252,0,0,0
main.o,0.338710%,244,0,244,0,0,0
lludivv7m.o,0.330381%,238,0,238,0,0,0
ldexp.o,0.316500%,228,0,228,0,0,0
rs485_app.o,0.288736%,208,8,200,0,8,0
gd32f4xx_dac.o,0.283184%,204,0,204,0,0,0
_printf_wctomb.o,0.272079%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.260973%,188,0,148,40,0,0
_printf_intcommon.o,0.247092%,178,0,178,0,0,0
gd32f4xx_misc.o,0.244316%,176,0,176,0,0,0
scheduler.o,0.238763%,172,100,72,0,100,0
strtod.o,0.227658%,164,0,164,0,0,0
_strtoul.o,0.219329%,158,0,158,0,0,0
dnaninf.o,0.216552%,156,0,156,0,0,0
strncmp.o,0.208223%,150,0,150,0,0,0
systick.o,0.205447%,148,4,144,0,4,0
frexp.o,0.194342%,140,0,140,0,0,0
fnaninf.o,0.194342%,140,0,140,0,0,0
led_app.o,0.192954%,139,7,132,0,7,0
rt_memcpy_v6.o,0.191566%,138,0,138,0,0,0
lludiv10.o,0.191566%,138,0,138,0,0,0
strcmpv7m.o,0.177684%,128,0,128,0,0,0
_printf_fp_infnan.o,0.177684%,128,0,128,0,0,0
_printf_longlong_dec.o,0.172131%,124,0,124,0,0,0
perfc_port_default.o,0.169355%,122,0,122,0,0,0
dleqf.o,0.166579%,120,0,120,0,0,0
deqf.o,0.166579%,120,0,120,0,0,0
_printf_dec.o,0.166579%,120,0,120,0,0,0
strtol.o,0.155474%,112,0,112,0,0,0
_printf_oct_int_ll.o,0.155474%,112,0,112,0,0,0
drleqf.o,0.149921%,108,0,108,0,0,0
gd32f4xx_spi.o,0.144368%,104,0,104,0,0,0
retnan.o,0.138816%,100,0,100,0,0,0
rt_memcpy_w.o,0.138816%,100,0,100,0,0,0
d2f.o,0.136039%,98,0,98,0,0,0
scalbn.o,0.127710%,92,0,92,0,0,0
__dczerorl2.o,0.124934%,90,0,90,0,0,0
memcmp.o,0.122158%,88,0,88,0,0,0
f2d.o,0.119381%,86,0,86,0,0,0
strncpy.o,0.119381%,86,0,86,0,0,0
_printf_str.o,0.113829%,82,0,82,0,0,0
rt_memclr_w.o,0.108276%,78,0,78,0,0,0
_printf_pad.o,0.108276%,78,0,78,0,0,0
sys_stackheap_outer.o,0.102724%,74,0,74,0,0,0
strcpy.o,0.099947%,72,0,72,0,0,0
llsdiv.o,0.099947%,72,0,72,0,0,0
lc_numeric_c.o,0.099947%,72,0,44,28,0,0
rt_memclr.o,0.094395%,68,0,68,0,0,0
dunder.o,0.088842%,64,0,64,0,0,0
_wcrtomb.o,0.088842%,64,0,64,0,0,0
_sgetc.o,0.088842%,64,0,64,0,0,0
strlen.o,0.086066%,62,0,62,0,0,0
__0sscanf.o,0.083289%,60,0,60,0,0,0
__2snprintf.o,0.077737%,56,0,56,0,0,0
vsnprintf.o,0.072184%,52,0,52,0,0,0
__scatter.o,0.072184%,52,0,52,0,0,0
fpclassify.o,0.066632%,48,0,48,0,0,0
trapv.o,0.066632%,48,0,48,0,0,0
_printf_char_common.o,0.066632%,48,0,48,0,0,0
scanf_char.o,0.061079%,44,0,44,0,0,0
_printf_wchar.o,0.061079%,44,0,44,0,0,0
_printf_char.o,0.061079%,44,0,44,0,0,0
__2sprintf.o,0.061079%,44,0,44,0,0,0
_printf_charcount.o,0.055526%,40,0,40,0,0,0
llshl.o,0.052750%,38,0,38,0,0,0
libinit2.o,0.052750%,38,0,38,0,0,0
init_aeabi.o,0.049974%,36,0,36,0,0,0
_printf_truncate.o,0.049974%,36,0,36,0,0,0
strtof.o,0.044421%,32,0,32,0,0,0
systick_wrapper_ual.o,0.044421%,32,0,32,0,0,0
strpbrk.o,0.041645%,30,0,30,0,0,0
_chval.o,0.038868%,28,0,28,0,0,0
__scatter_zi.o,0.038868%,28,0,28,0,0,0
strtof.o,0.036092%,26,0,26,0,0,0
atol.o,0.036092%,26,0,26,0,0,0
dcmpi.o,0.033316%,24,0,24,0,0,0
_rserrno.o,0.030539%,22,0,22,0,0,0
fabs.o,0.027763%,20,0,20,0,0,0
strchr.o,0.027763%,20,0,20,0,0,0
adc_app.o,0.027763%,20,0,20,0,0,0
isspace.o,0.024987%,18,0,18,0,0,0
exit.o,0.024987%,18,0,18,0,0,0
fpconst.o,0.022210%,16,0,0,16,0,0
dcheck1.o,0.022210%,16,0,16,0,0,0
rt_ctype_table.o,0.022210%,16,0,16,0,0,0
_snputc.o,0.022210%,16,0,16,0,0,0
gd32f4xx_pmu.o,0.022210%,16,0,16,0,0,0
__printf_wp.o,0.019434%,14,0,14,0,0,0
dretinf.o,0.016658%,12,0,12,0,0,0
sys_exit.o,0.016658%,12,0,12,0,0,0
__rtentry2.o,0.016658%,12,0,12,0,0,0
rtc_app.o,0.016658%,12,0,12,0,0,0
fretinf.o,0.013882%,10,0,10,0,0,0
fpinit.o,0.013882%,10,0,10,0,0,0
rtexit2.o,0.013882%,10,0,10,0,0,0
_sputc.o,0.013882%,10,0,10,0,0,0
_printf_ll.o,0.013882%,10,0,10,0,0,0
_printf_l.o,0.013882%,10,0,10,0,0,0
scanf2.o,0.011105%,8,0,8,0,0,0
rt_locale_intlibspace.o,0.011105%,8,0,8,0,0,0
rt_errno_addr_intlibspace.o,0.011105%,8,0,8,0,0,0
libspace.o,0.011105%,8,96,8,0,0,96
__main.o,0.011105%,8,0,8,0,0,0
istatus.o,0.008329%,6,0,6,0,0,0
heapauxi.o,0.008329%,6,0,6,0,0,0
_printf_x.o,0.008329%,6,0,6,0,0,0
_printf_u.o,0.008329%,6,0,6,0,0,0
_printf_s.o,0.008329%,6,0,6,0,0,0
_printf_p.o,0.008329%,6,0,6,0,0,0
_printf_o.o,0.008329%,6,0,6,0,0,0
_printf_n.o,0.008329%,6,0,6,0,0,0
_printf_ls.o,0.008329%,6,0,6,0,0,0
_printf_llx.o,0.008329%,6,0,6,0,0,0
_printf_llu.o,0.008329%,6,0,6,0,0,0
_printf_llo.o,0.008329%,6,0,6,0,0,0
_printf_lli.o,0.008329%,6,0,6,0,0,0
_printf_lld.o,0.008329%,6,0,6,0,0,0
_printf_lc.o,0.008329%,6,0,6,0,0,0
_printf_i.o,0.008329%,6,0,6,0,0,0
_printf_g.o,0.008329%,6,0,6,0,0,0
_printf_f.o,0.008329%,6,0,6,0,0,0
_printf_e.o,0.008329%,6,0,6,0,0,0
_printf_d.o,0.008329%,6,0,6,0,0,0
_printf_c.o,0.008329%,6,0,6,0,0,0
_printf_a.o,0.008329%,6,0,6,0,0,0
__rtentry4.o,0.008329%,6,0,6,0,0,0
scanf1.o,0.005553%,4,0,4,0,0,0
printf2.o,0.005553%,4,0,4,0,0,0
printf1.o,0.005553%,4,0,4,0,0,0
_printf_percent_end.o,0.005553%,4,0,4,0,0,0
use_no_semi.o,0.002776%,2,0,2,0,0,0
rtexit.o,0.002776%,2,0,2,0,0,0
libshutdown2.o,0.002776%,2,0,2,0,0,0
libshutdown.o,0.002776%,2,0,2,0,0,0
libinit.o,0.002776%,2,0,2,0,0,0
