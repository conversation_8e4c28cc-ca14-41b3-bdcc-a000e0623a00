#ifndef __USART_APP_H__
#define __USART_APP_H__

#include "stdint.h"
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// Command state enumeration
typedef enum {
    CMD_STATE_IDLE = 0,
    CMD_STATE_RTC_CONFIG,
    CMD_STATE_RATIO_CONFIG,
    CMD_STATE_LIMIT_CONFIG
} cmd_state_t;

int my_printf(uint32_t usart_periph, const char *format, ...);
void uart_task(void);

// BCD conversion functions
uint8_t dec_to_bcd(uint8_t dec);
uint8_t bcd_to_dec(uint8_t bcd);

// System self-test function
void system_selftest(void);

// RTC time configuration function
void rtc_config_handler(char *time_str);

// RTC current time display function
void rtc_show_current_time(void);

// Configuration management definitions
#define CONFIG_FLASH_ADDR 0x1F0000
#define CONFIG_MAGIC 0x12345678

// Output format enumeration
typedef enum {
    OUTPUT_FORMAT_NORMAL = 0,
    OUTPUT_FORMAT_HIDDEN = 1
} output_format_t;

// Configuration parameters structure
typedef struct {
    float ratio;        // 变比参数，范围0-100
    float limit;        // 阈值参数，范围0-200
    uint8_t sample_cycle; // 采样周期，有效值5/10/15秒
    uint8_t output_format; // 输出格式，0=NORMAL，1=HIDDEN
    bool encrypt_mode_enabled; // 加密模式开关
    uint32_t log_id;    // 日志ID（上电次数）
    bool log_id_user_set; // 标志：log_id是否由用户手动设置为0
    char device_id[32]; // 设备ID字符串，最大31字符+结束符
    uint32_t magic;     // 魔数，用于验证数据有效性
    uint32_t checksum;  // 校验和，确保数据完整性
} config_params_t;

// Configuration management functions
bool validate_ratio(float value);
bool validate_limit(float value);
bool validate_sample_cycle(uint8_t value);
bool validate_output_format(uint8_t value);
bool validate_encrypt_mode(bool value);
bool validate_log_id(uint32_t value);
float parse_float_input(char *input);
config_params_t* get_config_params(void);
output_format_t get_output_format(void);
void set_output_format(output_format_t format);
bool get_encrypt_mode(void);
void set_encrypt_mode(bool enabled);
uint32_t get_log_id(void);
void increment_log_id(void);
bool set_log_id(uint32_t new_log_id);
const char* get_device_id(void);
bool set_device_id(const char* device_id);
bool config_ini_file_exists(void);
bool create_config_ini_file(void);

// Flash storage functions
uint32_t calculate_checksum(config_params_t *params);
bool config_save_to_flash(void);
bool config_load_from_flash(void);

// INI file parsing functions
char* trim_whitespace(char *str);
bool parse_config_ini(char *content, config_params_t *params);

// Configuration file reading functions
void config_read_handler(void);

// Parameter setting functions
void ratio_config_handler(void);
void limit_config_handler(void);
void ratio_input_handler(char *input);
void limit_input_handler(char *input);

// Parameter storage functions
void config_save_handler(void);
void config_read_flash_handler(void);

// Sampling control functions
void sampling_start_handler(void);
void sampling_stop_handler(void);

#ifdef __cplusplus
}
#endif

#endif
