#include "mcu_cmic_gd32f470vet6.h"
#include "sampling_app.h"
#include "sd_app.h"
#include <math.h>

// External variables
extern uint16_t convertarr[CONVERT_NUM];

// Global sampling control variable
sampling_control_t g_sampling_control = {
    .state = SAMPLING_IDLE,
    .cycle_seconds = 5,             // Default 5 seconds cycle
    .last_sample_time = 0,
    .led_blink_time = 0,
    .current_voltage = 0.0f,
    .over_limit = false
};

// OLED display logic moved to oled_app.c

/*!
    \brief      get current sampling state
    \param[in]  none
    \param[out] none
    \retval     current sampling state
*/
sampling_state_t get_sampling_state(void)
{
    return g_sampling_control.state;
}

/*!
    \brief      get current sampling cycle
    \param[in]  none
    \param[out] none
    \retval     current sampling cycle in seconds
*/
uint8_t get_sampling_cycle(void)
{
    return g_sampling_control.cycle_seconds;
}

/*!
    \brief      get current voltage value
    \param[in]  none
    \param[out] none
    \retval     current voltage value
*/
float get_current_voltage_value(void)
{
    return g_sampling_control.current_voltage;
}

/*!
    \brief      check if voltage is over limit
    \param[in]  none
    \param[out] none
    \retval     true if over limit, false otherwise
*/
bool is_over_limit(void)
{
    return g_sampling_control.over_limit;
}

/*!
    \brief      convert ADC value to voltage
    \param[in]  adc_value: ADC raw value (0-4095)
    \param[out] none
    \retval     converted voltage value
*/
float adc_to_voltage(uint16_t adc_value)
{
    config_params_t *config = get_config_params();

    // ADC: 12-bit resolution, 3.3V reference voltage
    // Formula: voltage = (adc_value * 3.3V) / 4095 * ratio
    if(adc_value > 4095) {
        adc_value = 4095; // Clamp to maximum value
    }

    return (adc_value * 3.3f) / 4095.0f * config->ratio;
}

/*!
    \brief      get current voltage from ADC
    \param[in]  none
    \param[out] none
    \retval     current voltage value
*/
float get_current_voltage(void)
{
    uint16_t adc_value;

    // Get ADC value from convertarr[0]
    if(convertarr != NULL) {
        adc_value = convertarr[0];
    } else {
        adc_value = 0; // Default value if ADC not available
    }

    return adc_to_voltage(adc_value);
}

/*!
    \brief      convert BCD value to decimal
    \param[in]  bcd_value: BCD value to convert
    \param[out] none
    \retval     decimal value
*/
uint8_t bcd_to_dec(uint8_t bcd_value)
{
    return ((bcd_value >> 4) * 10) + (bcd_value & 0x0F);
}

/*!
    \brief      convert RTC month enum to decimal value
    \param[in]  rtc_month: RTC month enum value
    \param[out] none
    \retval     decimal month value (1-12)
*/
uint8_t rtc_month_to_decimal(uint8_t rtc_month)
{
    switch(rtc_month) {
        case 0x01: return 1;  // RTC_JAN
        case 0x02: return 2;  // RTC_FEB
        case 0x03: return 3;  // RTC_MAR
        case 0x04: return 4;  // RTC_APR
        case 0x05: return 5;  // RTC_MAY
        case 0x06: return 6;  // RTC_JUN
        case 0x07: return 7;  // RTC_JUL
        case 0x08: return 8;  // RTC_AUG
        case 0x09: return 9;  // RTC_SEP
        case 0x10: return 10; // RTC_OCT
        case 0x11: return 11; // RTC_NOV
        case 0x12: return 12; // RTC_DEC
        default: return 1; // Default to January
    }
}

/*!
    \brief      check if year is leap year
    \param[in]  year: year to check
    \param[out] none
    \retval     true if leap year, false otherwise
*/
static bool is_leap_year(int year)
{
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
}

/*!
    \brief      calculate Unix timestamp from date and time components
    \param[in]  year: year (e.g., 2025)
    \param[in]  month: month (1-12)
    \param[in]  day: day (1-31)
    \param[in]  hour: hour (0-23)
    \param[in]  minute: minute (0-59)
    \param[in]  second: second (0-59)
    \param[out] none
    \retval     Unix timestamp (seconds since 1970-01-01 00:00:00 UTC)
*/
static uint32_t calculate_unix_timestamp(int year, int month, int day, int hour, int minute, int second)
{
    // Days in each month (non-leap year)
    static const int days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    uint32_t days = 0;
    int i;

    // Calculate days from 1970 to the given year
    for(i = 1970; i < year; i++) {
        if(is_leap_year(i)) {
            days += 366;
        } else {
            days += 365;
        }
    }

    // Add days for months in the current year
    for(i = 1; i < month; i++) {
        days += days_in_month[i - 1];
        // Add extra day for February in leap year
        if(i == 2 && is_leap_year(year)) {
            days += 1;
        }
    }

    // Add days in the current month (subtract 1 because day is 1-based)
    days += (day - 1);

    // Convert to seconds and add time components
    return days * 86400UL + hour * 3600UL + minute * 60UL + second;
}

/*!
    \brief      convert RTC time to Unix timestamp
    \param[in]  rtc_time: pointer to RTC time structure
    \param[out] none
    \retval     Unix timestamp (seconds since 1970-01-01 00:00:00 UTC)
*/
uint32_t rtc_to_unix_timestamp(rtc_parameter_struct *rtc_time)
{
    if(rtc_time == NULL) {
        return 0;
    }

    // Convert BCD values to decimal
    int year = 2000 + bcd_to_dec(rtc_time->year);
    int month = rtc_month_to_decimal(rtc_time->month);
    int day = bcd_to_dec(rtc_time->date);
    int hour = bcd_to_dec(rtc_time->hour);
    int minute = bcd_to_dec(rtc_time->minute);
    int second = bcd_to_dec(rtc_time->second);

    // Calculate Unix timestamp
    return calculate_unix_timestamp(year, month, day, hour, minute, second);
}

/*!
    \brief      format Unix timestamp as 8-digit HEX string (big-endian)
    \param[in]  timestamp: Unix timestamp value
    \param[in]  hex_buffer: output buffer for HEX string (must be at least 9 bytes)
    \param[out] none
    \retval     none
*/
void format_timestamp_hex(uint32_t timestamp, char *hex_buffer)
{
    if(hex_buffer == NULL) {
        return;
    }

    // Format as 8-digit uppercase HEX (big-endian)
    sprintf(hex_buffer, "%08X", timestamp);
}

/*!
    \brief      validate voltage value range
    \param[in]  voltage: voltage value to validate
    \param[out] none
    \retval     true if valid, false otherwise
*/
static bool validate_voltage_range(float voltage)
{
    // Check for reasonable voltage range (0 to 999.99V)
    // Also check for NaN and infinity
    if(voltage < 0.0f || voltage > 999.99f || voltage != voltage) {
        return false;
    }
    return true;
}

/*!
    \brief      format voltage value as 8-digit HEX string
    \param[in]  voltage: voltage value to convert
    \param[in]  hex_buffer: output buffer for HEX string (must be at least 9 bytes)
    \param[out] none
    \retval     none
*/
void format_voltage_hex(float voltage, char *hex_buffer)
{
    if(hex_buffer == NULL) {
        return;
    }

    // Validate input voltage range
    if(!validate_voltage_range(voltage)) {
        // Set to zero for invalid values
        voltage = 0.0f;
    }

    // Split voltage into integer and fractional parts
    uint16_t integer_part = (uint16_t)voltage;
    float fractional = voltage - (float)integer_part;

    // Convert fractional part to 16-bit value (multiply by 65536)
    // Add 0.5 for proper rounding to avoid floating point precision issues
    uint16_t fractional_part = (uint16_t)(fractional * 65536.0f + 0.5f);

    // Handle overflow case where fractional rounding causes carry
    if(fractional_part == 0 && fractional > 0.99999f) {
        integer_part += 1;
        fractional_part = 0;
    }

    // Format as 8-digit uppercase HEX: integer_part(4 digits) + fractional_part(4 digits)
    sprintf(hex_buffer, "%04X%04X", integer_part, fractional_part);
}

/*!
    \brief      format RTC time for sample output (YYYY-MM-DD HH:MM:SS)
    \param[in]  buffer: output buffer for formatted time string
    \param[in]  rtc_time: pointer to RTC time structure
    \param[out] none
    \retval     none
*/
void format_sample_time(char *buffer, rtc_parameter_struct *rtc_time)
{
    if(buffer == NULL || rtc_time == NULL) {
        return;
    }

    sprintf(buffer, "20%02d-%02d-%02d %02d:%02d:%02d",
            bcd_to_dec(rtc_time->year),
            rtc_month_to_decimal(rtc_time->month),
            bcd_to_dec(rtc_time->date),
            bcd_to_dec(rtc_time->hour),
            bcd_to_dec(rtc_time->minute),
            bcd_to_dec(rtc_time->second));
}

/*!
    \brief      format RTC time for OLED display (HH:MM:SS)
    \param[in]  buffer: output buffer for formatted time string
    \param[in]  rtc_time: pointer to RTC time structure
    \param[out] none
    \retval     none
*/
void format_display_time(char *buffer, rtc_parameter_struct *rtc_time)
{
    if(buffer == NULL || rtc_time == NULL) {
        return;
    }

    sprintf(buffer, "%02d:%02d:%02d",
            bcd_to_dec(rtc_time->hour),
            bcd_to_dec(rtc_time->minute),
            bcd_to_dec(rtc_time->second));
}

/*!
    \brief      get current time string
    \param[in]  buffer: output buffer for formatted time string
    \param[in]  full_format: true for full format (YYYY-MM-DD HH:MM:SS), false for short format (HH:MM:SS)
    \param[out] none
    \retval     none
*/
void get_current_time_string(char *buffer, bool full_format)
{
    rtc_parameter_struct rtc_time;

    if(buffer == NULL) {
        return;
    }

    // Get current RTC time
    rtc_current_time_get(&rtc_time);

    if(full_format) {
        format_sample_time(buffer, &rtc_time);
    } else {
        format_display_time(buffer, &rtc_time);
    }
}

/*!
    \brief      update LED1 status for sampling indication
    \param[in]  none
    \param[out] none
    \retval     none
*/
void update_led_status(void)
{
    uint32_t current_time = (uint32_t)get_system_ms();

    if(g_sampling_control.state == SAMPLING_ACTIVE) {
        // LED1 blinks with 1 second period during sampling
        if((current_time - g_sampling_control.led_blink_time) >= 1000) {
            LED1_TOGGLE;
            g_sampling_control.led_blink_time = current_time;
        }
    } else {
        // LED1 off when sampling is stopped
        LED1_OFF;
        g_sampling_control.led_blink_time = current_time; // Reset blink timer
    }
}

/*!
    \brief      update LED2 status for over-limit indication
    \param[in]  none
    \param[out] none
    \retval     none
*/
void update_overlimit_led(void)
{
    if(g_sampling_control.over_limit) {
        LED2_ON;  // LED2 on when over limit
    } else {
        LED2_OFF; // LED2 off when normal
    }
}

/*!
    \brief      display sampling information - moved to oled_app.c
    \param[in]  none
    \param[out] none
    \retval     none
*/
// display_sampling_info function moved to oled_app.c

// display_idle_info function moved to oled_app.c

// update_oled_display function moved to oled_app.c

/*!
    \brief      check if it's time to perform sampling
    \param[in]  none
    \param[out] none
    \retval     true if sampling time reached, false otherwise
*/
bool is_sample_time(void)
{
    uint32_t current_time = (uint32_t)get_system_ms();
    uint32_t cycle_ms = g_sampling_control.cycle_seconds * 1000;

    return (current_time - g_sampling_control.last_sample_time) >= cycle_ms;
}

/*!
    \brief      check if voltage is over limit
    \param[in]  voltage: voltage value to check
    \param[out] none
    \retval     true if over limit, false otherwise
*/
bool check_over_limit(float voltage)
{
    config_params_t *config = get_config_params();
    return voltage > config->limit;
}

/*!
    \brief      output sample data to serial port
    \param[in]  voltage: voltage value to output
    \param[in]  over_limit: whether voltage is over limit
    \param[out] none
    \retval     none
*/
void output_sample_data(float voltage, bool over_limit)
{
    output_format_t format = get_output_format();

    if(format == OUTPUT_FORMAT_HIDDEN) {
        // HEX format output
        rtc_parameter_struct rtc_time;
        char timestamp_hex[9], voltage_hex[9];

        // Get current RTC time
        rtc_current_time_get(&rtc_time);

        // Convert to Unix timestamp and format as HEX
        uint32_t timestamp = rtc_to_unix_timestamp(&rtc_time);
        format_timestamp_hex(timestamp, timestamp_hex);

        // Format voltage as HEX
        format_voltage_hex(voltage, voltage_hex);

        // Output HEX format with optional over-limit marker
        if(over_limit) {
            my_printf(DEBUG_USART, "%s%s*\r\n", timestamp_hex, voltage_hex);
        } else {
            my_printf(DEBUG_USART, "%s%s\r\n", timestamp_hex, voltage_hex);
        }
    } else {
        // Normal format output (preserve original behavior)
        char time_str[32];
        config_params_t *config = get_config_params();

        // Get current time string (full format)
        get_current_time_string(time_str, true);

        if(over_limit) {
            // Output with OverLimit warning
            my_printf(DEBUG_USART, "%s ch0=%.2fV OverLimit(%.1f) !\r\n",
                      time_str, voltage, config->limit);
        } else {
            // Normal output
            my_printf(DEBUG_USART, "%s ch0=%.2fV\r\n",
                      time_str, voltage);
        }
    }
}

/*!
    \brief      perform sampling operation
    \param[in]  none
    \param[out] none
    \retval     none
*/
void perform_sample(void)
{
    float voltage;
    bool over_limit;
    uint32_t current_time = (uint32_t)get_system_ms();

    // Get current voltage value
    voltage = get_current_voltage();

    // Check over limit status
    over_limit = check_over_limit(voltage);

    // Update sampling control structure
    g_sampling_control.current_voltage = voltage;
    g_sampling_control.over_limit = over_limit;
    g_sampling_control.last_sample_time = current_time;

    // Output sample data
    output_sample_data(voltage, over_limit);

    // Store sample data to SD card
    if(over_limit) {
        storage_save_overlimit_data(voltage);
    }
    storage_save_sample_data(voltage, over_limit);
}

/*!
    \brief      sampling task main function
    \param[in]  none
    \param[out] none
    \retval     none
*/
void sampling_task(void)
{
    // Check if sampling is active and time to sample
    if(g_sampling_control.state == SAMPLING_ACTIVE) {
        if(is_sample_time()) {
            perform_sample();
        }
    }

    // Update LED status indicators
    update_led_status();
    update_overlimit_led();

    // OLED display is now handled by oled_task
}

/*!
    \brief      sampling start handler
    \param[in]  none
    \param[out] none
    \retval     none
*/
void sampling_start_handler(void)
{
    uint32_t current_time = (uint32_t)get_system_ms();

    // Set sampling state to active
    g_sampling_control.state = SAMPLING_ACTIVE;

    // Initialize sampling timestamp
    g_sampling_control.last_sample_time = current_time;
    g_sampling_control.led_blink_time = current_time;

    // Output startup messages
    my_printf(DEBUG_USART, "Periodic Sampling\r\n");
    my_printf(DEBUG_USART, "sample cycle: %ds\r\n", g_sampling_control.cycle_seconds);
}

/*!
    \brief      sampling stop handler
    \param[in]  none
    \param[out] none
    \retval     none
*/
void sampling_stop_handler(void)
{
    // Set sampling state to idle
    g_sampling_control.state = SAMPLING_IDLE;

    // Clear over limit status when stopping
    g_sampling_control.over_limit = false;

    // Turn off LED1 and LED2
    LED1_OFF;
    LED2_OFF;

    // Output stop message
    my_printf(DEBUG_USART, "Periodic Sampling STOP\r\n");
}

/*!
    \brief      toggle sampling state between IDLE and ACTIVE
    \param[in]  none
    \param[out] none
    \retval     none
*/
void toggle_sampling_state(void)
{
    if(g_sampling_control.state == SAMPLING_IDLE) {
        sampling_start_handler();
        // 记录按键启动采样日志
        char log_msg[64];
        sprintf(log_msg, "sample start - cycle %ds (key press)", g_sampling_control.cycle_seconds);
        storage_log_operation(log_msg);
    } else {
        sampling_stop_handler();
        // 记录按键停止采样日志
        storage_log_operation("sample stop (key press)");
    }
}

/*!
    \brief      set sampling cycle and output adjustment message
    \param[in]  cycle: sampling cycle in seconds (5/10/15)
    \param[out] none
    \retval     none
*/
void set_sampling_cycle(uint8_t cycle)
{
    // Validate cycle value
    if(cycle != 5 && cycle != 10 && cycle != 15) {
        return; // Invalid cycle value
    }

    // Update sampling cycle
    g_sampling_control.cycle_seconds = cycle;

    // Save configuration to Flash for persistence
    save_sampling_config();

    // Output adjustment message
    my_printf(DEBUG_USART, "sample cycle adjust :%ds\r\n", cycle);

    // 记录采样周期切换日志
    char log_msg[64];
    sprintf(log_msg, "cycle switch to %ds (key press)", cycle);
    storage_log_operation(log_msg);
}

/*!
    \brief      sampling button event handler
    \param[in]  btn: pointer to button structure
    \param[in]  evt: button event type
    \param[out] none
    \retval     none
*/
void sampling_btn_event_handler(ebtn_btn_t *btn, ebtn_evt_t evt)
{
    if(evt != EBTN_EVT_ONCLICK) {
        return; // Only handle click events
    }

    switch(btn->key_id) {
        case 0: // USER_BUTTON_0 - KEY1: toggle sampling state
            toggle_sampling_state();
            break;

        case 1: // USER_BUTTON_1 - KEY2: set 5 second cycle
            set_sampling_cycle(5);
            break;

        case 2: // USER_BUTTON_2 - KEY3: set 10 second cycle
            set_sampling_cycle(10);
            break;

        case 3: // USER_BUTTON_3 - KEY4: set 15 second cycle
            set_sampling_cycle(15);
            break;

        default:
            break;
    }
}

/*!
    \brief      load sampling configuration from config parameters
    \param[in]  none
    \param[out] none
    \retval     none
*/
void load_sampling_config(void)
{
    config_params_t *config = get_config_params();

    // Load sampling cycle from configuration
    if(validate_sample_cycle(config->sample_cycle)) {
        g_sampling_control.cycle_seconds = config->sample_cycle;
    } else {
        // Use default value if invalid
        g_sampling_control.cycle_seconds = 5;
        config->sample_cycle = 5;
    }
}

/*!
    \brief      save current sampling configuration to config parameters
    \param[in]  none
    \param[out] none
    \retval     none
*/
void save_sampling_config(void)
{
    config_params_t *config = get_config_params();

    // Save current sampling cycle to configuration
    config->sample_cycle = g_sampling_control.cycle_seconds;

    // Persist to Flash
    config_save_to_flash();
}

/*!
    \brief      sampling initialization
    \param[in]  none
    \param[out] none
    \retval     none
*/
void sampling_init(void)
{
    // Reset sampling control structure to default values
    g_sampling_control.state = SAMPLING_IDLE;
    g_sampling_control.cycle_seconds = 5;
    g_sampling_control.last_sample_time = 0;
    g_sampling_control.led_blink_time = 0;
    g_sampling_control.current_voltage = 0.0f;
    g_sampling_control.over_limit = false;

    // Load configuration from Flash
    load_sampling_config();
}
