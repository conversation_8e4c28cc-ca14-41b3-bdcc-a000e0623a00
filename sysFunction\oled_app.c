#include "mcu_cmic_gd32f470vet6.h"
#include "sampling_app.h"
#include <math.h>
#include <string.h>

extern uint16_t adc_value[1];

// OLED display state cache to avoid unnecessary updates
static sampling_state_t last_display_state = SAMPLING_IDLE;
static float last_display_voltage = -1.0f;
static char last_time_str[32] = "";
static bool oled_initialized = false;

/**
 * @brief	ʹ������printf�ķ�ʽ��ʾ�ַ�������ʾ6x8��С��ASCII�ַ�
 * @param x  Character position on the X-axis  range��0 - 127
 * @param y  Character position on the Y-axis  range��0 - 3
 * ���磺oled_printf(0, 0, "Data = %d", dat);
 **/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; // ��ʱ�洢��ʽ������ַ���
  va_list arg;      // �����ɱ����
  int len;          // �����ַ�������

  va_start(arg, format);
  // ��ȫ�ظ�ʽ���ַ����� buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}

/*!
    \brief      clear specific line on OLED display
    \param[in]  y: line number (0-3)
    \param[out] none
    \retval     none
*/
void oled_clear_line(uint8_t y)
{
    // Clear entire line by writing spaces
    OLED_ShowStr(0, y, "                ", 8); // 16 spaces to clear 128 pixels
}

/*!
    \brief      display sampling information on OLED
    \param[in]  none
    \param[out] none
    \retval     none
*/
void display_sampling_info(void)
{
    char time_str[32];
    char voltage_str[16];
    float current_voltage = get_current_voltage_value();

    // Get current time string (display format)
    get_current_time_string(time_str, false);

    // Format voltage string
    snprintf(voltage_str, sizeof(voltage_str), "%.2f V", current_voltage);

    // Clear first line completely to prevent character residue
    oled_clear_line(0);

    // Display time on first line (y=0)
    oled_printf(0, 0, "%s", time_str);

    // Clear second line completely to prevent character residue
    oled_clear_line(2);

    // Display voltage on second line (y=2)
    oled_printf(0, 2, "%s", voltage_str);
}

/*!
    \brief      display idle information on OLED
    \param[in]  none
    \param[out] none
    \retval     none
*/
void display_idle_info(void)
{
    // Clear first line completely to prevent character residue
    oled_clear_line(0);

    // Display "system idle" on first line
    oled_printf(0, 0, "system idle");

    // Clear second line completely
    oled_clear_line(2);
}

void oled_task(void)
{
    sampling_state_t current_state = get_sampling_state();
    float current_voltage = get_current_voltage_value();
    char time_str[32];

    // Get current time string for comparison
    get_current_time_string(time_str, false);

    // Check if display update is needed
    bool need_update = false;

    // Force update on first run to ensure "system idle" is displayed at startup
//    if(!oled_initialized) {
//        need_update = true;
//        oled_initialized = true;
//        last_display_state = current_state;
//    }

    // Check state change
    if(current_state != last_display_state) {
        need_update = true;
        last_display_state = current_state;
    }

    // Check voltage change (only in sampling state)
    if(current_state == SAMPLING_ACTIVE) {
        if(fabs(current_voltage - last_display_voltage) > 0.01f) {
            need_update = true;
            last_display_voltage = current_voltage;
        }

        // Check time change
        if(strcmp(time_str, last_time_str) != 0) {
            need_update = true;
            strcpy(last_time_str, time_str);
        }
    }

    // Update display if needed
    if(need_update) {
        if(current_state == SAMPLING_ACTIVE) {
            display_sampling_info();
        } else {
            display_idle_info();
            // Reset cache when switching to idle
            last_display_voltage = -1.0f;
            strcpy(last_time_str, "");
        }
    }
}

/* CUSTOM EDIT */
