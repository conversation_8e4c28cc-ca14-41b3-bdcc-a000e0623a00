#ifndef __RS485_APP_H__
#define __RS485_APP_H__

#include "stdint.h"
#include <stdbool.h>
#include <stdarg.h>

#ifdef __cplusplus
extern "C" {
#endif

// RS485接收标志和缓冲区声明
extern volatile uint8_t rs485_rx_flag;
extern uint8_t rs485_rxbuffer[512];
extern uint8_t rs485_dma_buffer[512];

// RS485应用层函数声明
int rs485_printf(const char *format, ...);
void rs485_send_string(const char *str);
void rs485_task(void);

#ifdef __cplusplus
}
#endif

#endif /* __RS485_APP_H__ */
