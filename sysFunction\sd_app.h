#ifndef __SD_APP_H_
#define __SD_APP_H_

#include "stdint.h"
#include "stdbool.h"
#include "ff.h"

// Storage system type definitions
typedef enum {
    STORAGE_TYPE_SAMPLE = 0,    // 正常采样数据
    STORAGE_TYPE_OVERLIMIT,     // 超阈值数据
    STORAGE_TYPE_LOG,           // 操作日志
    STORAGE_TYPE_HIDEDATA       // 加密数据
} storage_type_t;

// File manager structure for each storage type
typedef struct {
    char current_filename[64];  // 当前文件名
    uint32_t record_count;      // 当前文件记录数
    bool file_open;            // 文件是否打开
    FIL file_handle;           // FATFS文件句柄
} file_manager_t;

// Global storage system state
typedef struct {
    file_manager_t managers[4]; // 四种存储类型的文件管理器
    bool sd_available;          // SD卡可用状态
    bool initialized;           // 初始化状态
} storage_system_t;

// Storage system function declarations
bool storage_init(void);
bool storage_is_available(void);
void storage_deinit(void);

// Storage interface function declarations
bool storage_save_sample_data(float voltage, bool over_limit);
bool storage_save_overlimit_data(float voltage);
bool storage_log_operation(const char* operation_desc);

// Encrypt mode control function declarations
bool storage_set_encrypt_mode(bool enabled);
bool storage_get_encrypt_mode(void);

// Storage system testing and monitoring function declarations
bool storage_test(void);
void storage_get_status(void);
bool storage_shutdown(void);

// Directory and file naming function declarations (internal use)
// These functions are static in sd_app.c, no external declarations needed

// Legacy functions
void sd_fatfs_init(void);
void sd_fatfs_test(void);

#endif /* __SD_APP_H_ */
